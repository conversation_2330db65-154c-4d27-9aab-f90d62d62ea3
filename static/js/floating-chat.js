/**
 * Floating Chatbot Widget
 * Provides a floating action button and slide-in chat panel
 */

class FloatingChatWidget {
    constructor() {
        this.isOpen = false;
        this.currentStoreId = null;
        this.conversationId = null;
        this.stores = [];
        this.isInitialized = false;
        
        this.init();
    }

    async init() {
        if (this.isInitialized) return;
        
        // Check authentication
        if (!this.checkAuth()) return;
        
        // Create widget HTML
        this.createWidget();
        
        // Load stores
        await this.loadStores();
        
        // Setup event listeners
        this.setupEventListeners();
        
        this.isInitialized = true;
    }

    checkAuth() {
        const token = localStorage.getItem('access_token');
        return token && token.trim() !== '';
    }

    createWidget() {
        // Create overlay for click-outside-to-close
        const overlay = document.createElement('div');
        overlay.id = 'chat-overlay';
        overlay.className = 'chat-overlay';
        overlay.style.display = 'none';
        
        // Create FAB button
        const fab = document.createElement('button');
        fab.id = 'chat-fab';
        fab.className = 'chat-fab';
        fab.innerHTML = `
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
            </svg>
        `;
        
        // Create chat widget panel
        const widget = document.createElement('div');
        widget.id = 'floating-chat-widget';
        widget.className = 'floating-chat-widget';
        widget.innerHTML = `
            <div class="chat-widget-header">
                <div class="chat-widget-title">
                    <h3>💬 Chat Assistant</h3>
                    <select id="floating-store-selector" class="floating-store-selector">
                        <option value="">Select a store...</option>
                    </select>
                </div>
                <button id="chat-close-btn" class="chat-close-btn">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="chat-widget-messages" id="floating-chat-messages">
                <div class="chat-welcome-message">
                    <h4>Welcome to your AI Shopping Assistant!</h4>
                    <p>Select a store above to start chatting with your store-specific assistant.</p>
                </div>
            </div>
            <div class="chat-widget-input">
                <div class="chat-input-container">
                    <textarea 
                        id="floating-chat-input" 
                        placeholder="Type your message..." 
                        rows="1"
                        disabled
                    ></textarea>
                    <button id="floating-send-btn" class="chat-send-btn" disabled>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                    </button>
                </div>
            </div>
        `;
        
        // Add elements to page
        document.body.appendChild(overlay);
        document.body.appendChild(fab);
        document.body.appendChild(widget);
    }

    setupEventListeners() {
        // FAB click
        document.getElementById('chat-fab').addEventListener('click', () => {
            this.toggleWidget();
        });
        
        // Close button click
        document.getElementById('chat-close-btn').addEventListener('click', () => {
            this.closeWidget();
        });
        
        // Overlay click (click outside to close)
        document.getElementById('chat-overlay').addEventListener('click', () => {
            this.closeWidget();
        });
        
        // Store selector change
        document.getElementById('floating-store-selector').addEventListener('change', (e) => {
            this.selectStore(e.target.value);
        });
        
        // Send button click
        document.getElementById('floating-send-btn').addEventListener('click', () => {
            this.sendMessage();
        });
        
        // Enter key in input
        document.getElementById('floating-chat-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // Auto-resize textarea
        document.getElementById('floating-chat-input').addEventListener('input', (e) => {
            this.autoResizeTextarea(e.target);
        });
    }

    toggleWidget() {
        if (this.isOpen) {
            this.closeWidget();
        } else {
            this.openWidget();
        }
    }

    openWidget() {
        this.isOpen = true;
        const widget = document.getElementById('floating-chat-widget');
        const overlay = document.getElementById('chat-overlay');
        const fab = document.getElementById('chat-fab');
        
        widget.classList.add('active');
        overlay.style.display = 'block';
        fab.style.display = 'none';
        
        // Focus on input if store is selected
        if (this.currentStoreId) {
            setTimeout(() => {
                document.getElementById('floating-chat-input').focus();
            }, 300);
        }
    }

    closeWidget() {
        this.isOpen = false;
        const widget = document.getElementById('floating-chat-widget');
        const overlay = document.getElementById('chat-overlay');
        const fab = document.getElementById('chat-fab');
        
        widget.classList.remove('active');
        overlay.style.display = 'none';
        fab.style.display = 'flex';
    }

    async loadStores() {
        try {
            const response = await fetch('/assistants/stores/selection', {
                headers: this.getAuthHeaders()
            });
            
            if (response.ok) {
                this.stores = await response.json();
                this.populateStoreSelector();
            } else {
                console.error('Failed to load stores');
            }
        } catch (error) {
            console.error('Error loading stores:', error);
        }
    }

    populateStoreSelector() {
        const selector = document.getElementById('floating-store-selector');
        selector.innerHTML = '<option value="">Select a store...</option>';
        
        this.stores.forEach(store => {
            const option = document.createElement('option');
            option.value = store.store_id;
            option.textContent = `${store.store_name} (${store.has_assistant ? 'Ready' : 'No Assistant'})`;
            option.disabled = !store.has_assistant;
            selector.appendChild(option);
        });
    }

    selectStore(storeId) {
        if (!storeId) {
            this.currentStoreId = null;
            this.conversationId = null;
            this.disableInput();
            this.clearMessages();
            this.showWelcomeMessage();
            return;
        }
        
        this.currentStoreId = parseInt(storeId);
        this.conversationId = null;
        
        const store = this.stores.find(s => s.store_id === this.currentStoreId);
        if (store && store.has_assistant) {
            this.enableInput();
            this.clearMessages();
            this.addAssistantMessage(`Hello! I'm the AI assistant for ${store.store_name}. How can I help you today?`);
        } else {
            this.showError('This store does not have an assistant yet. Please connect the store first.');
        }
    }

    enableInput() {
        document.getElementById('floating-chat-input').disabled = false;
        document.getElementById('floating-send-btn').disabled = false;
    }

    disableInput() {
        document.getElementById('floating-chat-input').disabled = true;
        document.getElementById('floating-send-btn').disabled = true;
    }

    async sendMessage() {
        const input = document.getElementById('floating-chat-input');
        const message = input.value.trim();
        
        if (!message || !this.currentStoreId) return;
        
        // Add user message
        this.addUserMessage(message);
        input.value = '';
        this.autoResizeTextarea(input);
        
        // Show typing indicator
        this.showTypingIndicator();
        
        try {
            const response = await fetch('/assistants/chat', {
                method: 'POST',
                headers: this.getAuthHeaders(),
                body: JSON.stringify({
                    message: message,
                    store_id: this.currentStoreId,
                    conversation_id: this.conversationId
                })
            });
            
            this.hideTypingIndicator();
            
            if (response.ok) {
                const data = await response.json();
                this.conversationId = data.conversation_id;
                this.addAssistantMessage(data.response);
            } else {
                const error = await response.json();
                this.showError(error.detail || 'Failed to send message');
            }
        } catch (error) {
            this.hideTypingIndicator();
            this.showError('Network error while sending message');
        }
    }

    addUserMessage(message) {
        const messagesContainer = document.getElementById('floating-chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message user-message';
        messageDiv.innerHTML = `
            <div class="message-content">
                ${this.escapeHtml(message)}
                <div class="message-time">${new Date().toLocaleTimeString()}</div>
            </div>
        `;
        messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    addAssistantMessage(message) {
        const messagesContainer = document.getElementById('floating-chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'chat-message assistant-message';
        messageDiv.innerHTML = `
            <div class="message-content">
                ${this.escapeHtml(message)}
                <div class="message-time">${new Date().toLocaleTimeString()}</div>
            </div>
        `;
        messagesContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        const messagesContainer = document.getElementById('floating-chat-messages');
        const typingDiv = document.createElement('div');
        typingDiv.id = 'floating-typing-indicator';
        typingDiv.className = 'chat-message assistant-message typing-indicator';
        typingDiv.innerHTML = `
            <div class="message-content">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        `;
        messagesContainer.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = document.getElementById('floating-typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    clearMessages() {
        const messagesContainer = document.getElementById('floating-chat-messages');
        messagesContainer.innerHTML = '';
    }

    showWelcomeMessage() {
        const messagesContainer = document.getElementById('floating-chat-messages');
        messagesContainer.innerHTML = `
            <div class="chat-welcome-message">
                <h4>Welcome to your AI Shopping Assistant!</h4>
                <p>Select a store above to start chatting with your store-specific assistant.</p>
            </div>
        `;
    }

    showError(message) {
        // You could implement a toast notification or inline error display
        console.error('Chat Error:', message);
        this.addAssistantMessage(`Error: ${message}`);
    }

    scrollToBottom() {
        const messagesContainer = document.getElementById('floating-chat-messages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    autoResizeTextarea(textarea) {
        textarea.style.height = 'auto';
        textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    getAuthHeaders() {
        const token = localStorage.getItem('access_token');
        return {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        };
    }
}

// Initialize floating chat widget when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if user is authenticated
    const token = localStorage.getItem('access_token');
    if (token && token.trim() !== '') {
        new FloatingChatWidget();
    }
});
