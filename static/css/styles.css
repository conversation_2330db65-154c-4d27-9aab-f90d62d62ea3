:root {
    --primary-color: #4a90e2;
    --secondary-color: #f5f5f5;
    --accent-color: #ff6b6b;
    --text-color: #333;
    --light-text: #666;
    --border-color: #ddd;
    --assistant-bg: #f1f1f1;
    --user-bg: #4a90e2;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f9f9f9;
    padding: 20px;
    position: relative;
    min-height: 100vh;
}

.site-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Floating Chat Overlay */
.chat-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 998;
    backdrop-filter: blur(2px);
}

/* Floating <PERSON> Button (FAB) */
.chat-fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 999;
    transition: all 0.3s ease;
}

.chat-fab:hover {
    background-color: #3a7bc8;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.chat-fab svg {
    width: 24px;
    height: 24px;
}

/* Floating Chat Widget */
.floating-chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 400px;
    height: 600px;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 1000;
    transform: translateX(420px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-chat-widget.active {
    transform: translateX(0);
    opacity: 1;
}

/* Chat Widget Header */
.chat-widget-header {
    background-color: var(--primary-color);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    min-height: 60px;
}

.chat-widget-title {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
}

.chat-widget-title h3 {
    font-size: 16px;
    margin: 0;
    font-weight: 600;
}

.floating-store-selector {
    padding: 6px 10px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 12px;
    max-width: 200px;
}

.floating-store-selector option {
    background: var(--primary-color);
    color: white;
}

.chat-close-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    transition: opacity 0.3s;
    padding: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
}

.chat-close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Chat Widget Messages */
.chat-widget-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    gap: 16px;
    scrollbar-width: thin;
}

.chat-widget-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-widget-messages::-webkit-scrollbar-thumb {
    background-color: #d1d1d1;
    border-radius: 3px;
}

.chat-widget-messages::-webkit-scrollbar-track {
    background-color: #f1f1f1;
}

.chat-welcome-message {
    text-align: center;
    padding: 20px;
    color: var(--light-text);
}

.chat-welcome-message h4 {
    margin: 0 0 10px 0;
    color: var(--text-color);
    font-size: 16px;
}

.chat-welcome-message p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
}

/* Chat Messages */
.chat-message {
    display: flex;
    flex-direction: column;
    margin-bottom: 12px;
}

.chat-message.user-message {
    align-items: flex-end;
}

.chat-message.assistant-message {
    align-items: flex-start;
}

.chat-message .message-content {
    max-width: 85%;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

.user-message .message-content {
    background-color: var(--primary-color);
    color: white;
    border-bottom-right-radius: 6px;
}

.assistant-message .message-content {
    background-color: #f1f1f1;
    color: var(--text-color);
    border-bottom-left-radius: 6px;
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
}

/* Typing Indicator */
.typing-indicator .message-content {
    background-color: #f1f1f1;
    padding: 12px 16px;
}

.typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.typing-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #999;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Chat Widget Input */
.chat-widget-input {
    border-top: 1px solid #eaeaea;
    background-color: white;
    padding: 16px 20px;
}

.chat-input-container {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

#floating-chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    outline: none;
    font-size: 14px;
    color: #333;
    resize: none;
    min-height: 20px;
    max-height: 100px;
    font-family: inherit;
    line-height: 1.4;
}

#floating-chat-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.1);
}

#floating-chat-input:disabled {
    background-color: #f5f5f5;
    color: #999;
    cursor: not-allowed;
}

.chat-send-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s;
    flex-shrink: 0;
}

.chat-send-btn:hover:not(:disabled) {
    background-color: #3a7bc8;
}

.chat-send-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .site-content {
        padding: 10px;
    }

    .floating-chat-widget {
        width: 100%;
        height: 100%;
        bottom: 0;
        right: 0;
        left: 0;
        top: 0;
        border-radius: 0;
        transform: translateY(100%);
    }

    .floating-chat-widget.active {
        transform: translateY(0);
    }

    .chat-widget-header {
        border-radius: 0;
        padding: 16px;
    }

    .chat-fab {
        bottom: 20px;
        right: 20px;
    }

    .chat-message .message-content {
        max-width: 90%;
    }

    .floating-store-selector {
        max-width: 150px;
    }
}

@media (max-width: 480px) {
    .chat-fab {
        width: 56px;
        height: 56px;
        bottom: 16px;
        right: 16px;
    }

    .chat-fab svg {
        width: 20px;
        height: 20px;
    }

    .chat-widget-header {
        padding: 12px 16px;
        min-height: 56px;
    }

    .chat-widget-title h3 {
        font-size: 14px;
    }

    .floating-store-selector {
        font-size: 11px;
        padding: 4px 8px;
        max-width: 120px;
    }

    .chat-widget-messages {
        padding: 16px;
    }

    .chat-widget-input {
        padding: 12px 16px;
    }
}
