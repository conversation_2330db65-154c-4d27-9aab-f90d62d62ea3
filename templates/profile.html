<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Settings - Pinecone Assistant</title>
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        .profile-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .profile-section {
            background: white;
            border-radius: 8px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .profile-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .profile-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff, #0056b3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 48px;
            color: white;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }

        .form-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        .form-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }

        .btn:hover {
            opacity: 0.9;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .alert {
            padding: 12px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #007bff;
            text-decoration: none;
        }

        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <a href="/dashboard" class="back-link">← Back to Dashboard</a>

        <div class="profile-section">
            <div class="profile-header">
                <div class="profile-avatar">
                    👤
                </div>
                <h1>Profile Settings</h1>
                <p>Manage your account information and preferences</p>
            </div>

            <div id="success-message" class="alert alert-success"></div>
            <div id="error-message" class="alert alert-error"></div>
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>Updating profile...</p>
            </div>

            <form id="profile-form">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="full-name">Full Name (Optional)</label>
                    <input type="text" id="full-name" name="full_name">
                </div>

                <div class="form-actions">
                    <a href="/dashboard" class="btn btn-secondary">Cancel</a>
                    <button type="submit" class="btn btn-primary" id="save-btn">Save Changes</button>
                </div>
            </form>
        </div>

        <div class="profile-section">
            <h2>Change Password</h2>
            <form id="password-form">
                <div class="form-group">
                    <label for="current-password">Current Password</label>
                    <input type="password" id="current-password" name="current_password" required>
                </div>

                <div class="form-group">
                    <label for="new-password">New Password</label>
                    <input type="password" id="new-password" name="new_password" required>
                </div>

                <div class="form-group">
                    <label for="confirm-password">Confirm New Password</label>
                    <input type="password" id="confirm-password" name="confirm_password" required>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="password-btn">Change Password</button>
                </div>
            </form>
        </div>

        <div class="profile-section">
            <h2>Account Information</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #666;">Account Status</h4>
                    <span class="status-badge status-active">Active</span>
                </div>
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #666;">Member Since</h4>
                    <p id="member-since" style="margin: 0;">Loading...</p>
                </div>
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #666;">Connected Stores</h4>
                    <p id="stores-count" style="margin: 0; font-size: 24px; font-weight: bold; color: #007bff;">0</p>
                </div>
                <div>
                    <h4 style="margin: 0 0 10px 0; color: #666;">Active Assistants</h4>
                    <p id="assistants-count" style="margin: 0; font-size: 24px; font-weight: bold; color: #28a745;">0</p>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/auth.js"></script>
    <script src="/static/js/floating-chat.js"></script>
    <script>
        // Load user profile data
        function loadProfileData() {
            const userData = JSON.parse(localStorage.getItem('user_data') || '{}');

            if (userData.username) {
                document.getElementById('username').value = userData.username;
                document.getElementById('email').value = userData.email;
                document.getElementById('full-name').value = userData.full_name || '';

                if (userData.created_at) {
                    document.getElementById('member-since').textContent = formatDate(userData.created_at);
                }
            }
        }

        // Format date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // Load account stats
        async function loadAccountStats() {
            try {
                // Load stores count
                const storesResponse = await fetch('/stores/', {
                    headers: getAuthHeaders()
                });
                if (storesResponse.ok) {
                    const stores = await storesResponse.json();
                    document.getElementById('stores-count').textContent = stores.length;
                }

                // Load assistants count
                const assistantsResponse = await fetch('/assistants/', {
                    headers: getAuthHeaders()
                });
                if (assistantsResponse.ok) {
                    const assistants = await assistantsResponse.json();
                    document.getElementById('assistants-count').textContent = assistants.length;
                }
            } catch (error) {
                console.error('Error loading account stats:', error);
            }
        }

        // Handle profile form submission
        document.getElementById('profile-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const profileData = {
                username: formData.get('username'),
                email: formData.get('email'),
                full_name: formData.get('full_name')
            };

            showLoading(true);
            hideMessages();

            try {
                const response = await fetch('/auth/profile', {
                    method: 'PUT',
                    headers: {
                        ...getAuthHeaders(),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(profileData)
                });

                if (response.ok) {
                    const updatedUser = await response.json();

                    // Update localStorage
                    localStorage.setItem('user_data', JSON.stringify(updatedUser));

                    showSuccess('Profile updated successfully!');
                } else {
                    const error = await response.json();
                    showError(error.detail || 'Failed to update profile');
                }
            } catch (error) {
                showError('Network error while updating profile');
            } finally {
                showLoading(false);
            }
        });

        // Handle password form submission
        document.getElementById('password-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const newPassword = formData.get('new_password');
            const confirmPassword = formData.get('confirm_password');

            if (newPassword !== confirmPassword) {
                showError('New passwords do not match');
                return;
            }

            const passwordData = {
                current_password: formData.get('current_password'),
                new_password: newPassword
            };

            showLoading(true);
            hideMessages();

            try {
                const response = await fetch('/auth/change-password', {
                    method: 'POST',
                    headers: {
                        ...getAuthHeaders(),
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(passwordData)
                });

                if (response.ok) {
                    showSuccess('Password changed successfully!');
                    document.getElementById('password-form').reset();
                } else {
                    const error = await response.json();
                    showError(error.detail || 'Failed to change password');
                }
            } catch (error) {
                showError('Network error while changing password');
            } finally {
                showLoading(false);
            }
        });

        // Utility functions
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
            document.getElementById('save-btn').disabled = show;
            document.getElementById('password-btn').disabled = show;
        }

        function showSuccess(message) {
            const successDiv = document.getElementById('success-message');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('error-message').style.display = 'none';

            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 5000);
        }

        function showError(message) {
            const errorDiv = document.getElementById('error-message');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('success-message').style.display = 'none';
        }

        function hideMessages() {
            document.getElementById('success-message').style.display = 'none';
            document.getElementById('error-message').style.display = 'none';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Require authentication
            if (!requireAuth()) return;

            loadProfileData();
            loadAccountStats();
        });
    </script>
</body>
</html>
